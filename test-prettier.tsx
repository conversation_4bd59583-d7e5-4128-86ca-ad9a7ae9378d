// 이 파일은 Prettier 테스트용입니다
import React from 'react';
import { useState, useEffect } from 'react';

export default function TestComponent() {
  const [count, setCount] = useState(0);
  const [name, setName] = useState('test');

  useEffect(() => {
    console.log('Component mounted');
  }, []);

  const handleClick = () => {
    setCount(count + 1);
  };

  return (
    <div className="test-component">
      <h1>Test Component</h1>
      <p>Count: {count}</p>
      <p>Name: {name}</p>
      <button onClick={handleClick}>Increment</button>
    </div>
  );
}
