---
type: "agent_requested"
description: "Example description"
---
# Claude 지시사항

## 언어 설정
- 모든 응답과 설명을 한글로 제공해주세요
- 코드 주석은 한글로 작성해주세요
- 변수명과 함수명은 영어로 유지하되, 설명은 한글로 해주세요
- 에러 메시지나 로그 설명도 한글로 제공해주세요

## Next.js 15 사용 조건
- 이 프로젝트는 Next.js 15를 사용합니다
- App Router를 기본으로 사용하며, Pages Router는 사용하지 않습니다
- Server Components와 Client Components를 적절히 구분하여 사용해주세요
- `async/await` 문법을 Server Components에서 적극 활용해주세요
- `generateMetadata` 함수는 async 함수로 작성해주세요 (Next.js 15 요구사항)
- `headers()`, `cookies()` 등의 함수는 await를 사용해서 호출해주세요
- Turbopack을 개발 서버에서 사용할 수 있습니다 (`next dev --turbo`)
- React 19의 새로운 기능들을 활용할 수 있습니다
- 타입스크립트를 기본으로 사용하며, 엄격한 타입 체크를 적용해주세요