'use client'
import Script from 'next/script'

/**
 * Next.js 15 App Router에서는 메타데이터를 컴포넌트에서 직접 반환하지 않습니다.
 * 대신 layout.tsx에서 metadata 객체를 export하거나 generateMetadata 함수를 사용해야 합니다.
 *
 * 이 컴포넌트는 외부 스크립트와 스타일시트만 로드하는 용도로 변경합니다.
 */
export default function MainHeader() {
  return (
    <>
      {/* 외부 폰트 로드 */}
      <link
        href="https://cdn.jsdelivr.net/gh/sun-typeface/SUIT@2/fonts/static/woff2/SUIT.css"
        rel="stylesheet"
      />

      {/* CSS 파일들 */}
      <link rel="stylesheet" type="text/css" href="/assets/css/jquery.fullpage.min.css" />
      <link rel="stylesheet" type="text/css" href="/assets/css/swiper.jquery.min.css" />
      <link rel="stylesheet" type="text/css" href="/assets/css/common.css" />

      {/* Next.js Script 컴포넌트 사용 (성능 최적화) */}
      <Script
        src="/assets/js/jquery-3.6.0.min.js"
        strategy="beforeInteractive"
      />
      <Script
        src="/assets/js/swiper-bundle.min.js"
        strategy="beforeInteractive"
      />
      <Script
        src="/assets/js/common.js"
        strategy="afterInteractive"
      />
      <Script
        src="/assets/js/scrolloverflow.js"
        strategy="afterInteractive"
      />
      <Script
        src="/assets/js/jquery.fullpage.min.js"
        strategy="afterInteractive"
      />
      <Script
        src="/assets/js/jquery.fullpage.extensions.min.js"
        strategy="afterInteractive"
      />
      <div className="header">
        <div className="inner">
          <h1>
            <a href="/">
              <span className="blind">gcube</span>
            </a>
          </h1>
          <div className="gnb">
            <button
              type="button"
              className="btn__nav"
              onClick={() => {
                // 모바일 GNB 토글 로직
                console.log('모바일 GNB 토글');
              }}
            >
              <span className="blind">모바일 GNB 열기</span>
            </button>
            <div className="nav">
              <div className="nav-top">
                <div className="logo">
                  <a href="/">
                    <span className="blind">gcube</span>
                  </a>
                </div>
                <button
                  type="button"
                  className="btn__gpu"
                  onClick={() => {
                    // GPU 관리 페이지로 이동 또는 모달 열기
                    console.log('GPU 관리 클릭');
                  }}
                >
                  GPU 관리
                </button>
              </div>
              <ul>
                <li><a href="/price">Price</a></li>
                <li><a href="/docs">Docs</a></li>
                <li><a href="/faq">FAQ</a></li>
                <li><a href="/contact">Contact</a></li>
              </ul>
              <button
                type="button"
                className="btn__gpu"
                onClick={() => {
                  // GPU 관리 페이지로 이동 또는 모달 열기
                  console.log('GPU 관리 클릭');
                }}
              >
                GPU 관리
              </button>
            </div>
            <button
              type="button"
              className="btn__gpu"
              onClick={() => {
                // GPU 관리 페이지로 이동 또는 모달 열기
                console.log('GPU 관리 클릭');
              }}
            >
              GPU 관리
            </button>
          </div>
        </div>
      </div>
    </>
  )
}
