export default function MainHeader() {
  return (
    <>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no"/>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
	<meta http-equiv="Content-Script-Type" content="text/javascript"/>
	<meta http-equiv="Content-Style-Type" content="text/css"/>
	<meta name="description" content=""/>
	<meta name="keywords" content=""/>
	<meta property="og:type" content="website"/>
	<meta property="og:url" content=""/>
	<meta property="og:title" content=""/>
	<meta property="og:image" content="assets/images/img.png"/>
	<meta property="og:image:width" content="1920"/>
	<meta property="og:image:height" content="1080"/>
	<meta property="og:description" content="AI 학습과 추론에 필수인 GPU! 지큐브에 무엇이든 물어보세요. NVIDIA GPU를 합리적인 가격으로 원할 때 사용 가능합니다."/>
	<meta property="og:site_name" content="지큐브"/>
	<meta property="og:locale" content="en_US" />
	<title>지큐브</title> 
	<link rel="shortcut icon" href="assets/favi/favicon.ico">
	<meta name="theme-color" content="#ffffff">
	<link href="https://cdn.jsdelivr.net/gh/sun-typeface/SUIT@2/fonts/static/woff2/SUIT.css" rel="stylesheet" />
	<link rel="stylesheet" type="text/css" href="assets/css/jquery.fullpage.min.css" />
	<link rel="stylesheet" type="text/css" href="assets/css/swiper.jquery.min.css" />
	<link rel="stylesheet" type="text/css" href="assets/css/common.css" />
	<script type="text/javascript" src="assets/js/jquery-3.6.0.min.js"></script>
	<script type="text/javascript" src="assets/js/swiper-bundle.min.js"></script>
	<script type="text/javascript" src="assets/js/common.js"></script>
	<script type="text/javascript" src="assets/js/scrolloverflow.js"></script>
	<script type="text/javascript" src="assets/js/jquery.fullpage.min.js"></script>
	<script type="text/javascript" src="assets/js/jquery.fullpage.extensions.min.js"></script>
	<script type="text/javascript" src="assets/js/swiper-bundle.min.js"></script>
    </>
    )
}
