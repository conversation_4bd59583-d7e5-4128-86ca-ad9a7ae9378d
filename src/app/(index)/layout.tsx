import { Metadata } from "next";
import { headers } from "next/headers";

export async function generateMetadata(): Promise<Metadata> {
  const headersList = await headers();

  const canonicalUrl = headersList.get('canonical') ?? '/';
  return {
    metadataBase: new URL('https://gcube.ai'),
    title: 'gcube | 지큐브 | 클라우드 GPU',
    description: 'AI 학습과 추론에 필수인 GPU! 지큐브에 무엇이든 물어보세요. NVIDIA GPU를 합리적인 가격으로 원할 때 사용 가능합니다.',
    keywords: ['GCUBE', 'gcube', '지큐브', 'NVIDIA GPU', 'AI'],
    openGraph: {
      title: 'gcube | 지큐브 | 클라우드 GPU',
      description: 'AI 학습과 추론에 필수인 GPU! 지큐브에 무엇이든 물어보세요. NVIDIA GPU를 합리적인 가격으로 원할 때 사용 가능합니다.'
    },
    alternates: {
      canonical: canonicalUrl
    }
  };
}

export default function IndexLayout({children,}: {children: React.ReactNode;}) {

  
  return (
    <div className="index-layout">
      {/* 메인 사이트 헤더 */}
      <header className="index-header">
        <nav className="flex items-center justify-between p-4 bg-white shadow-md">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-bold text-gray-800">GAI Platform</h1>
          </div>
          <div className="flex space-x-4">
            <a href="/about" className="text-gray-600 hover:text-gray-800">About</a>
            <a href="/features" className="text-gray-600 hover:text-gray-800">Features</a>
            <a href="/pricing" className="text-gray-600 hover:text-gray-800">Pricing</a>
            <button className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
              Get Started
            </button>
          </div>
        </nav>
      </header>

      {/* 메인 컨텐츠 영역 */}
      <main className="min-h-screen">
        {children}
      </main>

      {/* 메인 사이트 푸터 */}
      <footer className="bg-gray-50 border-t">
        <div className="max-w-7xl mx-auto py-12 px-4">
          <div className="grid grid-cols-4 gap-8">
            <div>
              <h3 className="font-semibold text-gray-800 mb-4">Product</h3>
              <ul className="space-y-2 text-gray-600">
                <li><a href="/features">Features</a></li>
                <li><a href="/pricing">Pricing</a></li>
                <li><a href="/docs">Documentation</a></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-gray-800 mb-4">Company</h3>
              <ul className="space-y-2 text-gray-600">
                <li><a href="/about">About</a></li>
                <li><a href="/blog">Blog</a></li>
                <li><a href="/careers">Careers</a></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-gray-800 mb-4">Support</h3>
              <ul className="space-y-2 text-gray-600">
                <li><a href="/help">Help Center</a></li>
                <li><a href="/contact">Contact</a></li>
                <li><a href="/status">Status</a></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-gray-800 mb-4">Legal</h3>
              <ul className="space-y-2 text-gray-600">
                <li><a href="/privacy">Privacy</a></li>
                <li><a href="/terms">Terms</a></li>
                <li><a href="/security">Security</a></li>
              </ul>
            </div>
          </div>
          <div className="mt-8 pt-8 border-t text-center text-gray-600">
            <p>&copy; 2025 GAI Platform. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
