'use client'

import { useEffect, useRef } from 'react'

// Swiper 타입 정의
declare global {
  interface Window {
    Swiper: any
  }
}

export default function MainSwiperContent() {
  const swiperRef = useRef<HTMLDivElement>(null)

  // Swiper 초기화 (전역 Swiper 라이브러리 사용)
  useEffect(() => {
    let mainSwiper: any = null

    // Swiper 라이브러리가 로드될 때까지 대기
    const initSwiper = () => {
      if (typeof window !== 'undefined' && window.Swiper && swiperRef.current) {
        mainSwiper = new window.Swiper(swiperRef.current, {
          speed: 1000,
          autoplay: {
            delay: 6000,
            disableOnInteraction: false,
          },
          loop: true,
          pagination: {
            el: ".mainSwiper .swiper-pagination",
            clickable: true,
          },
        })
      }
    }

    // Swiper가 이미 로드되어 있으면 바로 초기화
    if (window.Swiper) {
      initSwiper()
    } else {
      // Swiper 로드를 기다림
      const checkSwiper = setInterval(() => {
        if (window.Swiper) {
          clearInterval(checkSwiper)
          initSwiper()
        }
      }, 100)

      // 10초 후 타임아웃
      setTimeout(() => {
        clearInterval(checkSwiper)
      }, 10000)
    }

    // 컴포넌트 언마운트 시 Swiper 인스턴스 정리
    return () => {
      if (mainSwiper) {
        mainSwiper.destroy(true, true)
      }
    } 
  }, [])

  return (
    <>
      <div ref={swiperRef} className="swiper mainSwiper">
					<div className="swiper-wrapper">
						<div className="swiper-slide mv1">
							<div className="main-slide__content">
								<p>간편하게 공유하고 경제적으로 빌려쓰는</p>
								<h2>전 세계 GPU NETWORK</h2>
								<div>고정 비용이 아닌, 자원 사용량에 따라 비용을 부과하여 경제적으로<br />GPU를 사용할 수 있습니다.</div>
							</div>
						</div>
						<div className="swiper-slide mv2">
							<div className="main-slide__content">
								<p>사용한 만큼만 지불하는 합리적 비용</p>
								<h2>최대 70% 경제적인<br />GPU Cloud 플랫폼</h2>
								<div>고정 비용이 아닌, 자원 사용량에 따라 비용을 부과하여<br />경제적으로 GPU를 사용할 수 있습니다.</div>
							</div>
						</div>
					</div>
					<div className="swiper-pagination"></div>
      </div>
    </>
  )
}