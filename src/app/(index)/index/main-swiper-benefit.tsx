'use client';

import { useEffect, useRef, useState } from 'react';

// Swiper 타입 정의
declare global {
  interface Window {
    Swiper: any;
  }
}

export default function MainSwiperBenefit() {
  const swiperRef = useRef<HTMLDivElement>(null);
  const [benefitSwiper, setBenefitSwiper] = useState<any>(null);
  const [activeTimer, setActiveTimer] = useState<NodeJS.Timeout | null>(null);
  const [activeIndex, setActiveIndex] = useState(0);

  // 활성 애니메이션 추가 함수
  const addActiveAnimation = () => {
    // 이미 애니메이션이 실행 중이면 리턴
    if (activeTimer) return;

    const slides = document.querySelectorAll('.benefitSwiper .swiper-slide');
    if (slides.length === 0) return;

    slides.forEach((el) => el.classList.remove('active'));
    slides[0].classList.add('active');

    let currentIndex = 0;
    const timer = setInterval(() => {
      slides[currentIndex].classList.remove('active');
      currentIndex = (currentIndex + 1) % slides.length;
      slides[currentIndex].classList.add('active');
      setActiveIndex(currentIndex);
    }, 4000);

    setActiveTimer(timer);
    setActiveIndex(0);
  };

  // 활성 애니메이션 제거 함수
  const removeActiveAnimation = () => {
    if (activeTimer) {
      clearInterval(activeTimer);
      setActiveTimer(null);
    }
    const slides = document.querySelectorAll('.benefitSwiper .swiper-slide');
    slides.forEach((el) => el.classList.remove('active'));
    setActiveIndex(0);
  };

  // Swiper 활성화 함수
  const enableSwiper = () => {
    // 이미 Swiper가 활성화되어 있으면 리턴
    if (benefitSwiper) return;

    if (typeof window !== 'undefined' && window.Swiper && swiperRef.current) {
      const swiper = new window.Swiper(swiperRef.current, {
        pagination: {
          el: '.benefitSwiper .swiper-pagination',
          clickable: true
        },
        autoplay: {
          delay: 4000,
          disableOnInteraction: false
        }
      });
      setBenefitSwiper(swiper);
    }
  };

  // Swiper 비활성화 함수
  const disableSwiper = () => {
    if (benefitSwiper) {
      benefitSwiper.destroy(true, true);
      setBenefitSwiper(null);
    }

    // Swiper 관련 클래스 정리
    if (typeof window !== 'undefined' && swiperRef.current) {
      const swiperContainer = swiperRef.current;
      // Swiper가 추가한 클래스들 제거
      swiperContainer.classList.remove('swiper-initialized', 'swiper-horizontal');

      // wrapper와 slides의 스타일 초기화
      const wrapper = swiperContainer.querySelector('.swiper-wrapper');
      if (wrapper) {
        wrapper.removeAttribute('style');
      }

      const slides = swiperContainer.querySelectorAll('.swiper-slide');
      slides.forEach((slide) => {
        slide.removeAttribute('style');
      });
    }
  };

  // 반응형 Swiper 처리 함수
  const handleResponsiveSwiper = () => {
    if (typeof window !== 'undefined') {
      const isMobile = window.innerWidth <= 767;

      if (isMobile) {
        // 모바일: 애니메이션 제거 후 Swiper 활성화
        removeActiveAnimation();
        enableSwiper();
      } else {
        // 데스크톱: Swiper 제거 후 애니메이션 활성화
        disableSwiper();
        // 약간의 지연을 두고 애니메이션 시작 (DOM 정리 시간 확보)
        setTimeout(() => {
          addActiveAnimation();
        }, 100);
      }
    }
  };

  // 컴포넌트 마운트 시 초기화
  useEffect(() => {
    let cleanup: (() => void) | undefined;

    // Swiper 라이브러리 로드 대기
    const initSwiper = () => {
      handleResponsiveSwiper();

      // const handleResize = () => handleResponsiveSwiper();
      window.addEventListener('resize', handleResponsiveSwiper);

      cleanup = () => {
        window.removeEventListener('resize', handleResponsiveSwiper);
        removeActiveAnimation();
        disableSwiper();
      };
    };

    if (typeof window !== 'undefined') {
      if (window.Swiper) {
        initSwiper();
      } else {
        // Swiper 로드 대기
        const checkSwiper = setInterval(() => {
          if (window.Swiper) {
            clearInterval(checkSwiper);
            initSwiper();
          }
        }, 100);

        // 10초 후 타임아웃
        const timeout = setTimeout(() => {
          clearInterval(checkSwiper);
        }, 10000);

        cleanup = () => {
          clearInterval(checkSwiper);
          clearTimeout(timeout);
          removeActiveAnimation();
          disableSwiper();
        };
      }
    }

    // 컴포넌트 언마운트 시 정리
    return () => {
      if (cleanup) cleanup();
    };
  }, []);

  // activeIndex 변경 시 애니메이션 업데이트
  useEffect(() => {
    if (activeTimer && typeof window !== 'undefined') {
      const slides = document.querySelectorAll('.benefitSwiper .swiper-slide');
      if (slides.length > 0) {
        slides.forEach((el) => el.classList.remove('active'));
        slides[activeIndex]?.classList.add('active');
      }
    }
  }, [activeIndex, activeTimer]);

  return (
    <>
      <div ref={swiperRef} className="swiper benefitSwiper">
        <ul className="swiper-wrapper benefit__list">
          <li className="swiper-slide">
            <dl>
              <dt>
                <strong>Global GPU Grid</strong>
              </dt>
              <dd>
                gcube는 Global GPU네트워킹을 통해 클라우드 컴퓨팅 성능을 강력하게 유지하면서, 경제적인 가격에 공급하는 GPU 공유경제
                서비스입니다.
              </dd>
            </dl>
          </li>
          <li className="swiper-slide">
            <dl>
              <dt>
                어떤 환경에도 적용가능한
                <br />
                <strong>맞춤형 공급</strong>
              </dt>
              <dd>
                클라우드 네이티브 기술을 이용하여 CSP의 GPU와 PC의 GPU를 결합하여 동시접속이 많은 서비스, 수요가 일정하지 않은 경우 등
                다양한 맞춤형 서비스를 제공합니다.
              </dd>
            </dl>
          </li>
          <li className="swiper-slide">
            <dl>
              <dt>
                다수의 국내 GPU 자원
                <br />
                확보로 <strong>높은 안정성</strong>
              </dt>
              <dd>
                국내 기반 서비스를 시작하며 다수의 공급자를 확보하여 다양한 GPU 자원, 빠른 네트워크를 통한 안정적인 서비스를
                제공합니다.{' '}
              </dd>
            </dl>
          </li>
          <li className="swiper-slide">
            <dl>
              <dt>
                <strong>누구나 저렴하게</strong>
                <br />
                이용 할 수 있는 GPU
              </dt>
              <dd>공급자와의 직접 계약, 누구나 손쉽게 공급할 수 있는 기술로 공급 비용을 절감하여 보다 경제적으로 이용할 수 있습니다. </dd>
            </dl>
          </li>
        </ul>
        <div className="swiper-pagination"></div>
      </div>
    </>
  );
}
