'use client';

export default function MainSwiperBenefit() {
  return (
    <>
      <div className="swiper benefitSwiper">
        <ul className="swiper-wrapper benefit__list">
          <li className="swiper-slide">
            <dl>
              <dt>
                <strong>Global GPU Grid</strong>
              </dt>
              <dd>
                gcube는 Global GPU네트워킹을 통해 클라우드 컴퓨팅 성능을 강력하게 유지하면서, 경제적인 가격에 공급하는 GPU 공유경제
                서비스입니다.
              </dd>
            </dl>
          </li>
          <li className="swiper-slide">
            <dl>
              <dt>
                어떤 환경에도 적용가능한
                <br />
                <strong>맞춤형 공급</strong>
              </dt>
              <dd>
                클라우드 네이티브 기술을 이용하여 CSP의 GPU와 PC의 GPU를 결합하여 동시접속이 많은 서비스, 수요가 일정하지 않은 경우 등
                다양한 맞춤형 서비스를 제공합니다.
              </dd>
            </dl>
          </li>
          <li className="swiper-slide">
            <dl>
              <dt>
                다수의 국내 GPU 자원
                <br />
                확보로 <strong>높은 안정성</strong>
              </dt>
              <dd>
                국내 기반 서비스를 시작하며 다수의 공급자를 확보하여 다양한 GPU 자원, 빠른 네트워크를 통한 안정적인 서비스를
                제공합니다.{' '}
              </dd>
            </dl>
          </li>
          <li className="swiper-slide">
            <dl>
              <dt>
                <strong>누구나 저렴하게</strong>
                <br />
                이용 할 수 있는 GPU
              </dt>
              <dd>공급자와의 직접 계약, 누구나 손쉽게 공급할 수 있는 기술로 공급 비용을 절감하여 보다 경제적으로 이용할 수 있습니다. </dd>
            </dl>
          </li>
        </ul>
        <div className="swiper-pagination"></div>
      </div>
       <script>
							let benefitSwiper = null;
							let activeTimer = null;
							let activeIndex = 0;
							function addActiveAnimation() {
							  const slides = document.querySelectorAll('.benefitSwiper .swiper-slide');
							  if (slides.length === 0) return;
							  slides.forEach(el => el.classList.remove('active'));
							  slides[0].classList.add('active');
							  activeIndex = 0;
							  activeTimer = setInterval(() => {
								slides[activeIndex].classList.remove('active');
								activeIndex = (activeIndex + 1) % slides.length;
								slides[activeIndex].classList.add('active');
							  }, 4000);
							}
							function removeActiveAnimation() {
							  clearInterval(activeTimer);
							  const slides = document.querySelectorAll('.benefitSwiper .swiper-slide');
							  slides.forEach(el => el.classList.remove('active'));
							  activeIndex = 0;
							}
							//swiper동작
							function enableSwiper() {
							  if (!benefitSwiper) {
								benefitSwiper = new Swiper(".benefitSwiper", {
								  pagination: {
									el: ".benefitSwiper .swiper-pagination",
									clickable: true,
								  },
								  autoplay: {
									delay: 4000,
									disableOnInteraction: false,
								  },
								});
							  }
							}
							//swiper해제
							function disableSwiper() {
							  if (benefitSwiper) {
								benefitSwiper.destroy(true, true);
								benefitSwiper = null;
							  }
							}
							function handleResponsiveSwiper() {
							  if (window.innerWidth <= 767) {
								enableSwiper();
							  } else {
								disableSwiper();
								removeActiveAnimation();
								addActiveAnimation();
							  }
							}
							handleResponsiveSwiper();
							window.addEventListener('resize', handleResponsiveSwiper);
							window.addEventListener('beforeunload', removeActiveAnimation);
						</script>
    </>
  );
}
