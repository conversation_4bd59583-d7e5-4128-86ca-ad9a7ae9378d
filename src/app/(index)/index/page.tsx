export default function HomePage() {
  return (
    <div>
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 text-center">
          <h1 className="text-5xl font-bold mb-6">
            차세대 AI 플랫폼에 오신 것을 환영합니다
          </h1>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            강력한 AI 에이전트와 함께 업무 효율성을 극대화하고, 
            혁신적인 솔루션을 경험해보세요.
          </p>
          <div className="space-x-4">
            <button className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100">
              무료로 시작하기
            </button>
            <button className="border border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600">
              데모 보기
            </button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              주요 기능
            </h2>
            <p className="text-xl text-gray-600">
              AI의 힘으로 더 스마트하게 일하세요
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white p-8 rounded-lg shadow-md">
              <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mb-4">
                <span className="text-white text-xl">🤖</span>
              </div>
              <h3 className="text-xl font-semibold mb-4">지능형 AI 에이전트</h3>
              <p className="text-gray-600">
                고도로 훈련된 AI 에이전트가 복잡한 작업을 자동화하고 
                최적의 솔루션을 제공합니다.
              </p>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-md">
              <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mb-4">
                <span className="text-white text-xl">⚡</span>
              </div>
              <h3 className="text-xl font-semibold mb-4">실시간 처리</h3>
              <p className="text-gray-600">
                빠른 응답 시간과 실시간 데이터 처리로 
                즉각적인 결과를 얻을 수 있습니다.
              </p>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-md">
              <div className="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mb-4">
                <span className="text-white text-xl">🔒</span>
              </div>
              <h3 className="text-xl font-semibold mb-4">보안 우선</h3>
              <p className="text-gray-600">
                엔터프라이즈급 보안으로 데이터를 안전하게 보호하고 
                개인정보를 철저히 관리합니다.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600 text-white">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold mb-6">
            지금 바로 시작해보세요
          </h2>
          <p className="text-xl mb-8">
            무료 체험으로 AI 플랫폼의 강력한 기능을 경험해보세요.
          </p>
          <button className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100">
            무료 체험 시작
          </button>
        </div>
      </section>
    </div>
  );
}
