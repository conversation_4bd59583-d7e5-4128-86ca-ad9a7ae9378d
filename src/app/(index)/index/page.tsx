export default function HomePage() {
  return (
    <>
   <div className="visual">
				
			</div>
			<div className="main__content main__content1">
				<div className="inner">
					<h2><span>최소 비용으로 효율적인 GPU 사용</span></h2>
					<div className="info--wrap benefit">
						<h3>
							 <div>성능은 좋지만 높은 비용, <span>비용은 낮지만 부족한 성능.</span></div>
							 <div>GPU 공유 서비스의 <span>한계를 극복했습니다.</span></div>
						</h3>
						<div className="swiper benefitSwiper">
							<ul className="swiper-wrapper benefit__list">
								<li className="swiper-slide">
									<dl>
										<dt><strong>Global GPU Grid</strong></dt>
										<dd>gcube는 Global GPU네트워킹을 통해 클라우드 컴퓨팅 성능을 강력하게 유지하면서, 경제적인 가격에 공급하는 GPU 공유경제 서비스입니다.</dd>
									</dl>
								</li>
								<li className="swiper-slide">
									<dl>
										<dt>어떤 환경에도 적용가능한<br /><strong>맞춤형 공급</strong></dt>
										<dd>클라우드 네이티브 기술을 이용하여 CSP의 GPU와 PC의 GPU를 결합하여 동시접속이 많은 서비스, 수요가 일정하지 않은 경우 등 다양한 맞춤형 서비스를 제공합니다.</dd>
									</dl>
								</li>
								<li className="swiper-slide">
									<dl>
										<dt>다수의 국내 GPU 자원<br />확보로 <strong>높은 안정성</strong></dt>
										<dd>국내 기반 서비스를 시작하며 다수의 공급자를 확보하여 다양한 GPU 자원, 빠른 네트워크를 통한 안정적인 서비스를 제공합니다. </dd>
									</dl>
								</li>
								<li className="swiper-slide">
									<dl>
										<dt><strong>누구나 저렴하게</strong><br />이용 할 수 있는 GPU</dt>
										<dd>공급자와의 직접 계약, 누구나 손쉽게 공급할 수 있는 기술로 공급 비용을 절감하여 보다 경제적으로 이용할 수 있습니다. </dd>
									</dl>
								</li>
							</ul>
							<div className="swiper-pagination"></div>
						</div>
						{/* <script>
							let benefitSwiper = null;
							let activeTimer = null;
							let activeIndex = 0;
							function addActiveAnimation() {
							  const slides = document.querySelectorAll('.benefitSwiper .swiper-slide');
							  if (slides.length === 0) return;
							  slides.forEach(el => el.classList.remove('active'));
							  slides[0].classList.add('active');
							  activeIndex = 0;
							  activeTimer = setInterval(() => {
								slides[activeIndex].classList.remove('active');
								activeIndex = (activeIndex + 1) % slides.length;
								slides[activeIndex].classList.add('active');
							  }, 4000);
							}
							function removeActiveAnimation() {
							  clearInterval(activeTimer);
							  const slides = document.querySelectorAll('.benefitSwiper .swiper-slide');
							  slides.forEach(el => el.classList.remove('active'));
							  activeIndex = 0;
							}
							//swiper동작
							function enableSwiper() {
							  if (!benefitSwiper) {
								benefitSwiper = new Swiper(".benefitSwiper", {
								  pagination: {
									el: ".benefitSwiper .swiper-pagination",
									clickable: true,
								  },
								  autoplay: {
									delay: 4000,
									disableOnInteraction: false,
								  },
								});
							  }
							}
							//swiper해제
							function disableSwiper() {
							  if (benefitSwiper) {
								benefitSwiper.destroy(true, true);
								benefitSwiper = null;
							  }
							}
							function handleResponsiveSwiper() {
							  if (window.innerWidth <= 767) {
								enableSwiper();
							  } else {
								disableSwiper();
								removeActiveAnimation();
								addActiveAnimation();
							  }
							}
							handleResponsiveSwiper();
							window.addEventListener('resize', handleResponsiveSwiper);
							window.addEventListener('beforeunload', removeActiveAnimation);
						</script> */}
					</div>
					<div className="info--wrap efficiency">
						<h3>
							 <div>최대 70% 경제적으로 <span>사용하는 GPU.</span></div>
							 <div>비용 걱정 없이 사용한 만큼만 <span>이용하세요.</span></div>
						</h3>
						<div className="efficiency--wrap">
							<div className="graph--wrap">
								<h4>시간당 GPU 사용 비용</h4>
								<ul className="graph__list other">
									<li>
										<dl>
											<dd className="other"><div className="bar"><div>39,746원</div></div></dd>
											<dd className="mine"><div className="bar"><div>4,830원</div></div></dd>
											<dt>V100</dt>
										</dl>
									</li>
									<li>
										<dl>
											<dd className="other"><div className="bar"><div>137,646원</div></div></dd>
											<dd className="mine"><div className="bar"><div>34,720원</div></div></dd>
											<dt>T4</dt>
										</dl>
									</li>
									<li>
										<dl>
											<dd className="other"><div className="bar"><div>57,344원</div></div></dd>
											<dd className="mine"><div className="bar"><div>20,790원</div></div></dd>
											<dt>RTX 4090</dt>
										</dl>
									</li>
									<li>
										<dl>
											<dd className="other"><div className="bar"><div>34,272원</div></div></dd>
											<dd className="mine"><div className="bar"><div>1,610원</div></div></dd>
											<dt>RTX 4080</dt>
										</dl>
									</li>
								</ul>
							</div>
							<div className="comparison--wrap">
								<button className="comparison__title"><span>GPU를 선택하세요</span></button>
								<ul className="comparison__list">
									<li>RTX 4090 사용 시</li>
									<li>RTX 4090 사용 시</li>
									<li>RTX 4090 사용 시</li>
									<li>RTX 4090 사용 시</li>
									<li>RTX 4090 사용 시</li>
									<li>RTX 4090 사용 시</li>
								</ul>
								<dl>
									<dt>1개월 기준</dt>
									<dd>
										<strong><span className="price">??</span>만원 부터</strong> 이용할 수 있습니다. 
									</dd>
								</dl>
								<p className="comparison__info">gcube는 GPU 이용 시 사용하지 않은 시간은 비용을 부과하지 않습니다. 실시간 모니터링을 통해 실제 리소스 사용 비율에 따라 비용이 부과됩니다.</p>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div className="main__content main__content2">
				<div className="inner">
					<h2><span>경제적이고 다양한 GPU</span></h2>
					<div className="info--wrap variety">
						<h3 className="pc-only">
							 <div>gcube에서 원하는 목적과 스펙의</div>
							 <div>다양한 GPU를 선택할 수 있습니다.</div>
						</h3>
						<h3 className="mo-only">
							 <div>gcube에서 원하는</div>
							 <div>목적과 스펙의 다양한 GPU를</div>
							 <div>선택할 수 있습니다.</div>
						</h3>
					</div>
				</div>
			</div>
			<div className="main__content main__content3"><h2>섹션3</h2><h3>섹션3</h3></div>
		
    </> 
  );
}
