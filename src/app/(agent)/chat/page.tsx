export default function ChatPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">AI Chat</h1>
        <button className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
          New Chat
        </button>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="space-y-4">
          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm">
              AI
            </div>
            <div className="flex-1 bg-gray-100 rounded-lg p-3">
              <p>안녕하세요! 무엇을 도와드릴까요?</p>
            </div>
          </div>

          <div className="flex items-start space-x-3 justify-end">
            <div className="flex-1 bg-blue-600 text-white rounded-lg p-3 max-w-xs">
              <p>Next.js에서 layout을 어떻게 사용하나요?</p>
            </div>
            <div className="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center text-white text-sm">
              U
            </div>
          </div>
        </div>

        <div className="mt-6 flex items-center space-x-2">
          <input
            type="text"
            placeholder="메시지를 입력하세요..."
            className="flex-1 border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <button className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
            전송
          </button>
        </div>
      </div>
    </div>
  );
}
